<step title="Incoming Request" date="2025-07-24T03:31:33.020" instance="e7f14f" url="/api/settings" method="POST" type="request" pid="820,1,5" Host="sentrycoin-v4-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Content-Type="application/json" Expect="100-continue" Content-Length="24" x-ms-request-id="3ce74e48-9ba3-4904-b658-1472e327f8b9" X-ARR-LOG-ID="3ce74e48-9ba3-4904-b658-1472e327f8b9" CLIENT-IP="*************:49154" X-SITE-DEPLOYMENT-ID="sentrycoin-v4-app" WAS-DEFAULT-HOSTNAME="sentrycoin-v4-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/api/settings" X-Client-IP="*************" X-Client-Port="49154" >
  <step title="Acquiring Deployment Lock" date="2025-07-24T03:31:33.258" /><!-- duration: 4ms -->
  <step title="Acquired Deployment Lock" date="2025-07-24T03:31:33.301" /><!-- duration: 3ms -->
  <step title="Releasing Deployment Lock" date="2025-07-24T03:31:33.358" /><!-- duration: 3ms -->
  <step title="Outgoing response" date="2025-07-24T03:31:33.377" type="response" statusCode="204" statusText="NoContent" /><!-- duration: 4ms -->
</step><!-- duration: 364ms -->

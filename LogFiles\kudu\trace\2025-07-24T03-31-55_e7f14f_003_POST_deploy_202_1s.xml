<step title="Incoming Request" date="2025-07-24T03:31:55.639" instance="e7f14f" url="/deploy?scmType=ExternalGit&amp;isAsync=true" method="POST" type="request" pid="820,1,15" Host="sentrycoin-v4-app.scm.azurewebsites.net" User-Agent="Azure-WebSites-Controller/***********" Authorization="Bas..." Expect="100-continue" Content-Length="87" x-ms-request-id="3ce74e48-9ba3-4904-b658-1472e327f8b9" X-ARR-LOG-ID="3ce74e48-9ba3-4904-b658-1472e327f8b9" CLIENT-IP="*************:49154" X-SITE-DEPLOYMENT-ID="sentrycoin-v4-app" WAS-DEFAULT-HOSTNAME="sentrycoin-v4-app.scm.azurewebsites.net" X-MS-PLATFORM-INTERNAL="True" X-Forwarded-Proto="https" X-AppService-Proto="https" X-Forwarded-TlsVersion="1.3" X-WAWS-Unencoded-URL="/deploy?scmType=ExternalGit&amp;isAsync=true" X-Client-IP="*************" X-Client-Port="49154" >
  <step title="FetchHandler" date="2025-07-24T03:31:55.664" >
    <step title="GitExeRepository.Initialize" date="2025-07-24T03:31:55.684" >
      <step title="Executing external process" date="2025-07-24T03:31:55.688" type="process" path="git" arguments="init" /><!-- duration: 297ms -->
      <step title="Executing external process" date="2025-07-24T03:31:55.989" type="process" path="git" arguments="config core.autocrlf false" /><!-- duration: 22ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.012" type="process" path="git" arguments="config core.preloadindex true" /><!-- duration: 22ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.037" type="process" path="git" arguments="config user.name &quot;unknown&quot;" /><!-- duration: 23ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.063" type="process" path="git" arguments="config user.email &quot;unknown&quot;" /><!-- duration: 19ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.084" type="process" path="git" arguments="config filter.lfs.clean &quot;git-lfs clean %f&quot;" /><!-- duration: 21ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.108" type="process" path="git" arguments="config filter.lfs.smudge &quot;git-lfs smudge %f&quot;" /><!-- duration: 27ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.137" type="process" path="git" arguments="config filter.lfs.required true" /><!-- duration: 24ms -->
      <step title="Configure git server" date="2025-07-24T03:31:56.164" >
        <step title="Executing external process" date="2025-07-24T03:31:56.166" type="process" path="git" arguments="config receive.denyCurrentBranch ignore" /><!-- duration: 20ms -->
      </step><!-- duration: 25ms -->
      <step title="Create deny users for .git folder" date="2025-07-24T03:31:56.191" /><!-- duration: 32ms -->
      <step title="Configure git-credential" date="2025-07-24T03:31:56.225" >
        <step title="Executing external process" date="2025-07-24T03:31:56.247" type="process" path="git" arguments="config credential.helper &quot;!&apos;/home/<USER>/repository/.git/hooks/git-credential-invalid.sh&apos;&quot;" /><!-- duration: 24ms -->
      </step><!-- duration: 48ms -->
      <step title="Setup post receive hook" date="2025-07-24T03:31:56.276" >
        <step title="Non-Windows enviroment, granting 755 permission to post-receive hook file" date="2025-07-24T03:31:56.305" /><!-- duration: 19ms -->
      </step><!-- duration: 50ms -->
    </step><!-- duration: 644ms -->
    <step title="Executing external process" date="2025-07-24T03:31:56.331" type="process" path="git" arguments="log -n 1 main --" >
      <step title="Process dump" date="2025-07-24T03:31:56.359" exitCode="128" type="processOutput" /><!-- duration: 2ms -->
    </step><!-- duration: 32ms -->
    <step title="Start deployment in the background" date="2025-07-24T03:31:56.368" >
      <step title="Executing external process" date="2025-07-24T03:31:56.379" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 18ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.405" type="process" path="git" arguments="rev-parse --git-dir" /><!-- duration: 16ms -->
      <step title="Executing external process" date="2025-07-24T03:31:56.423" type="process" path="git" arguments="log -n 1 main --" >
        <step title="Process dump" date="2025-07-24T03:31:56.450" exitCode="128" type="processOutput" /><!-- duration: 2ms -->
      </step><!-- duration: 32ms -->
      <step title="Acquiring Deployment Lock" date="2025-07-24T03:31:56.457" /><!-- duration: 2ms -->
      <step title="Acquired Deployment Lock" date="2025-07-24T03:31:56.498" /><!-- duration: 2ms -->
    </step><!-- duration: 203ms -->
  </step><!-- duration: 912ms -->
  <step title="Outgoing response" date="2025-07-24T03:31:56.578" type="response" statusCode="202" statusText="Accepted" /><!-- duration: 2ms -->
</step><!-- duration: 943ms -->

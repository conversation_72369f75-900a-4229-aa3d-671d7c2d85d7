<step title="BackgroundTrace" date="2025-07-24T03:31:56.370" instance="e7f14f" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-24T03:31:56.503" /><!-- duration: 2ms -->
  <step title="Creating temporary deployment" date="2025-07-24T03:31:56.508" /><!-- duration: 60ms -->
  <step title="Performing fetch based deployment" date="2025-07-24T03:31:56.709" >
    <step title="Deployment timer started" date="2025-07-24T03:31:59.956" >
      <step title="DeploymentManager.Deploy(id:d48ae81a988efb86482d918f0f6f4783ec7fac84)" date="2025-07-24T03:31:59.962" >
        <step title="Collecting changeset information" date="2025-07-24T03:31:59.996" /><!-- duration: 91ms -->
        <step title="Updating submodules" date="2025-07-24T03:32:00.092" /><!-- duration: 1095ms -->
        <step title="Determining deployment builder" date="2025-07-24T03:32:01.381" >
          <step title="Builder is OryxBuilder" date="2025-07-24T03:32:01.390" /><!-- duration: 5ms -->
        </step><!-- duration: 19ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-24T03:32:01.434" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-24T03:32:01.529" >
            <step title="Building" date="2025-07-24T03:32:01.649" >
              <step title="Executing external process" date="2025-07-24T03:32:01.877" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddca62a702ae2c -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 39209ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-24T03:32:41.359" /><!-- duration: 5ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-24T03:32:42.280" /><!-- duration: 8ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-24T03:32:42.316" /><!-- duration: 4ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-24T03:32:42.327" /><!-- duration: 3ms -->
            </step><!-- duration: 41261ms -->
          </step><!-- duration: 41393ms -->
          <step title="Cleaning up temp files" date="2025-07-24T03:32:42.925" /><!-- duration: 389ms -->
          <step title="Cleaning up temp files" date="2025-07-24T03:32:43.319" /><!-- duration: 511ms -->
          <step title="Reloading status file with latest updates" date="2025-07-24T03:32:43.857" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-24T03:32:43.862" /><!-- duration: 128ms -->
          </step><!-- duration: 140ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-24T03:32:44.010" /><!-- duration: 3ms -->
        </step><!-- duration: 42621ms -->
      </step><!-- duration: 44118ms -->

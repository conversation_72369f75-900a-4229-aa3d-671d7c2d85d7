<step title="BackgroundTrace" date="2025-07-24T03:35:02.702" instance="41d990" url="/deploy" method="POST" >
  <step title="Creating temporary deployment - FetchDeploymentManager" date="2025-07-24T03:35:02.847" /><!-- duration: 2ms -->
  <step title="Creating temporary deployment" date="2025-07-24T03:35:02.852" /><!-- duration: 51ms -->
  <step title="Performing fetch based deployment" date="2025-07-24T03:35:03.030" >
    <step title="Deployment timer started" date="2025-07-24T03:35:05.577" >
      <step title="DeploymentManager.Deploy(id:d48ae81a988efb86482d918f0f6f4783ec7fac84)" date="2025-07-24T03:35:05.583" >
        <step title="Collecting changeset information" date="2025-07-24T03:35:05.600" /><!-- duration: 100ms -->
        <step title="Updating submodules" date="2025-07-24T03:35:05.751" /><!-- duration: 1087ms -->
        <step title="Determining deployment builder" date="2025-07-24T03:35:07.044" >
          <step title="Builder is OryxBuilder" date="2025-07-24T03:35:07.050" /><!-- duration: 3ms -->
        </step><!-- duration: 14ms -->
        <step title="PreDeployment: context.CleanOutputPath False" date="2025-07-24T03:35:07.094" >
          <step title="PreDeployment: context.OutputPath /home/<USER>/wwwroot" date="2025-07-24T03:35:07.177" >
            <step title="Building" date="2025-07-24T03:35:07.274" >
              <step title="Executing external process" date="2025-07-24T03:35:07.467" type="process" path="bash" arguments="-c &quot;oryx build /home/<USER>/repository -o /home/<USER>/wwwroot --platform nodejs --platform-version 20 -p virtualenv_name= --log-file /tmp/build-debug.log  -i /tmp/8ddca6315ab9074 -p compress_node_modules=tar-gz | tee /tmp/oryx-build.log ; exit $PIPESTATUS &quot;" /><!-- duration: 17174ms -->
              <step title="Using GenericDeploymentLogParser" date="2025-07-24T03:35:24.957" /><!-- duration: 3ms -->
              <step title="Triggering recycle (preview mode disabled)." date="2025-07-24T03:35:25.773" /><!-- duration: 6ms -->
              <step title="Modified file to trigger a restart of the app container." date="2025-07-24T03:35:25.807" /><!-- duration: 3ms -->
              <step title="Skip function trigger and logicapp sync because function is not enabled." date="2025-07-24T03:35:25.819" /><!-- duration: 3ms -->
            </step><!-- duration: 19128ms -->
          </step><!-- duration: 19232ms -->
          <step title="Cleaning up temp files" date="2025-07-24T03:35:26.411" /><!-- duration: 261ms -->
          <step title="Cleaning up temp files" date="2025-07-24T03:35:26.674" /><!-- duration: 504ms -->
          <step title="Reloading status file with latest updates" date="2025-07-24T03:35:27.200" >
            <step title="WebHooksManager.PublishEventAsync: PostDeployment" date="2025-07-24T03:35:27.204" /><!-- duration: 39ms -->
          </step><!-- duration: 46ms -->
          <step title="Cleaning up temporary deployment - fetch deployment was successful" date="2025-07-24T03:35:27.252" /><!-- duration: 3ms -->
        </step><!-- duration: 20224ms -->
      </step><!-- duration: 21747ms -->
